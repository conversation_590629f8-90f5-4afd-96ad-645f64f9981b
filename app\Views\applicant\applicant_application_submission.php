<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="<?= base_url('applicant/jobs/position/' . $position['id']) ?>" class="btn btn-outline-primary mb-3">
                        <i class="fas fa-arrow-left me-2"></i>Back to Position Details
                    </a>
                    <h1 class="h3 mb-2">
                        Application Submission
                        <span class="badge bg-primary ms-2"><?= esc($position['position_reference']) ?></span>
                    </h1>
                    <p class="text-muted">
                        <strong>Position:</strong> <?= esc($position['designation']) ?><br>
                        <strong>Organization:</strong> <?= esc($organization['org_name']) ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Submission Process -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Process Steps -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-ol me-2"></i>Application Processing Steps
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div id="processSteps">
                                <div class="step-item" id="step1">
                                    <span class="step-number">1.</span>
                                    <span class="step-icon"><i class="fas fa-check text-success"></i></span>
                                    <span class="step-text">Profile Information: Collected from your profile</span>
                                </div>
                                <div class="step-item" id="step2">
                                    <span class="step-number">2.</span>
                                    <span class="step-icon"><i class="fas fa-check text-success"></i></span>
                                    <span class="step-text">Education Records: Retrieved from your education history</span>
                                </div>
                                <div class="step-item" id="step3">
                                    <span class="step-number">3.</span>
                                    <span class="step-icon"><i class="fas fa-check text-success"></i></span>
                                    <span class="step-text">Work Experience: Collected from your experience records</span>
                                </div>
                                <div class="step-item" id="step4">
                                    <span class="step-number">4.</span>
                                    <span class="step-icon"><i class="fas fa-check text-success"></i></span>
                                    <span class="step-text">Supporting Documents: Retrieved from your uploaded files</span>
                                </div>
                                <div class="step-item" id="step5">
                                    <span class="step-number">5.</span>
                                    <span class="step-icon"><i class="fas fa-clock text-warning"></i></span>
                                    <span class="step-text">Profile Analysis: Generate comprehensive applicant profile</span>
                                </div>
                                <div class="step-item" id="step6">
                                    <span class="step-number">6.</span>
                                    <span class="step-icon"><i class="fas fa-clock text-muted"></i></span>
                                    <span class="step-text">Final Submission: Submit application to organization</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Section -->
            <div class="card mb-4" id="analysisSection">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-check me-2"></i>Profile Analysis
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div id="analysisButton">
                        <p class="text-muted mb-4">
                            Click the button below to analyze your profile and generate a comprehensive application summary.
                        </p>
                        <button type="button" class="btn btn-primary btn-lg" id="analyzeProfileBtn">
                            <i class="fas fa-user-check me-2"></i>Analyse and Process My Application
                        </button>
                    </div>

                    <!-- Processing Status -->
                    <div id="processingStatus" style="display: none;">
                        <div class="mb-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <h6 id="processingTitle">Processing Your Application...</h6>
                        <p id="processingMessage" class="text-muted">Collecting your profile information...</p>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 style="width: 0%" 
                                 id="processingProgress">
                            </div>
                        </div>
                    </div>

                    <!-- Analysis Results -->
                    <div id="analysisResults" style="display: none;">
                        <div class="alert alert-success">
                            <h6 class="alert-heading">
                                <i class="fas fa-check-circle me-2"></i>Profile Analysis Complete
                            </h6>
                            <p class="mb-0">Your comprehensive application profile has been generated successfully.</p>
                        </div>

                        <!-- Profile Preview -->
                        <div class="text-start">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Generated Profile Summary</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="toggleProfileBtn">
                                    <i class="fas fa-eye me-1"></i>View Profile
                                </button>
                            </div>
                            
                            <div id="profilePreview" style="display: none;">
                                <div class="border rounded p-3 bg-light">
                                    <pre id="profileContent" class="mb-0" style="white-space: pre-wrap; font-size: 0.9em; max-height: 400px; overflow-y: auto;"></pre>
                                </div>
                            </div>
                        </div>

                        <!-- Final Submission Button -->
                        <div class="mt-4">
                            <button type="button" class="btn btn-success btn-lg" id="finalSubmitBtn">
                                <i class="fas fa-paper-plane me-2"></i>Your Application is Ready to be Submitted
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for final submission -->
<form id="finalSubmissionForm" action="<?= base_url('applicant/jobs/final-submission/' . $position['id']) ?>" method="post" style="display: none;">
    <?= csrf_field() ?>
    <input type="hidden" name="profile_data" id="hiddenProfileData">
</form>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.step-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.step-item:last-child {
    border-bottom: none;
}

.step-number {
    font-weight: bold;
    margin-right: 10px;
    min-width: 20px;
}

.step-icon {
    margin-right: 10px;
    min-width: 20px;
}

.step-text {
    flex: 1;
}

.progress {
    height: 8px;
}

#profileContent {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
let generatedProfile = null;

$(document).ready(function() {
    // Handle analyze profile button
    $('#analyzeProfileBtn').on('click', function() {
        analyzeProfile();
    });

    // Handle toggle profile view
    $('#toggleProfileBtn').on('click', function() {
        const preview = $('#profilePreview');
        const btn = $(this);
        
        if (preview.is(':visible')) {
            preview.hide();
            btn.html('<i class="fas fa-eye me-1"></i>View Profile');
        } else {
            preview.show();
            btn.html('<i class="fas fa-eye-slash me-1"></i>Hide Profile');
        }
    });

    // Handle final submission
    $('#finalSubmitBtn').on('click', function() {
        if (generatedProfile) {
            // Show confirmation dialog
            if (confirm('Are you sure you want to submit your application? This action cannot be undone.')) {
                $('#hiddenProfileData').val(JSON.stringify(generatedProfile));

                // Submit the form normally (not via AJAX to avoid CSRF issues)
                $('#finalSubmissionForm')[0].submit();
            }
        }
    });
});

async function analyzeProfile() {
    try {
        // Hide analyze button and show processing
        $('#analysisButton').hide();
        $('#processingStatus').show();
        
        // Update step 5 to processing
        updateStepStatus(5, 'processing', 'Profile Analysis: Starting analysis...');

        // Step 1: Collect applicant data
        updateProcessingStatus('Collecting your profile information...', 20);
        const applicantData = await collectApplicantData();

        // Step 2: Generate profile using code
        updateProcessingStatus('Generating comprehensive profile...', 40);
        const profileAnalysis = generateProfileWithCode(applicantData);

        // Step 3: Process results
        updateProcessingStatus('Processing analysis results...', 80);
        generatedProfile = profileAnalysis;

        // Step 4: Complete
        updateProcessingStatus('Analysis complete!', 100);

        // Update step 5 to complete
        updateStepStatus(5, 'complete', 'Profile Analysis: Comprehensive profile generated');
        updateStepStatus(6, 'ready', 'Final Submission: Ready to submit application');
        
        // Show results
        setTimeout(() => {
            $('#processingStatus').hide();
            $('#profileContent').text(JSON.stringify(profileAnalysis, null, 2));
            $('#analysisResults').show();
        }, 1000);
        
    } catch (error) {
        console.error('Error analyzing profile:', error);
        updateStepStatus(5, 'error', 'Profile Analysis: Error occurred during analysis');
        alert('Error analyzing profile: ' + error.message);
        
        // Reset UI
        $('#processingStatus').hide();
        $('#analysisButton').show();
    }
}

async function collectApplicantData() {
    const response = await fetch('<?= base_url('applicant/jobs/process-application/' . $position['id']) ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
        })
    });
    
    if (!response.ok) {
        throw new Error('Failed to collect applicant data');
    }
    
    return await response.json();
}

function generateProfileWithCode(applicantData) {
    const currentDate = new Date().toISOString();

    // Extract data from the response
    const data = applicantData.data || applicantData;
    const personalInfo = data.personal_info || {};
    const education = data.education || [];
    const experiences = data.experiences || [];
    const files = data.files || [];

    // Generate comprehensive profile
    const profile = {
        analysis_type: "comprehensive_profile",
        generated_at: currentDate,
        applicant_summary: {
            full_name: `${personalInfo.first_name || ''} ${personalInfo.last_name || ''}`.trim(),
            unique_id: personalInfo.unique_id || '',
            email: personalInfo.email || '',
            gender: personalInfo.gender || '',
            date_of_birth: personalInfo.dobirth || '',
            place_of_origin: personalInfo.place_of_origin || '',
            citizenship: personalInfo.citizenship || '',
            marital_status: personalInfo.marital_status || ''
        },
        personal_details: {
            contact_information: {
                contact_details: personalInfo.contact_details || '',
                location_address: personalInfo.location_address || '',
                email: personalInfo.email || ''
            },
            identification: {
                id_numbers: personalInfo.id_numbers || '',
                id_photo_path: personalInfo.id_photo_path || '',
                signature_path: personalInfo.signature_path || ''
            },
            family_information: {
                marital_status: personalInfo.marital_status || '',
                date_of_marriage: personalInfo.date_of_marriage || '',
                spouse_employer: personalInfo.spouse_employer || '',
                children: personalInfo.children || ''
            },
            employment_status: {
                is_public_servant: personalInfo.is_public_servant || false,
                public_service_file_number: personalInfo.public_service_file_number || '',
                employee_of_org_id: personalInfo.employee_of_org_id || '',
                current_employer: personalInfo.current_employer || '',
                current_position: personalInfo.current_position || '',
                current_salary: personalInfo.current_salary || ''
            },
            additional_information: {
                offence_convicted: personalInfo.offence_convicted || '',
                how_did_you_hear_about_us: personalInfo.how_did_you_hear_about_us || '',
                publications: personalInfo.publications || '',
                awards: personalInfo.awards || ''
            },
            referees: personalInfo.referees || ''
        },
        education_details: {
            total_qualifications: education.length,
            qualifications: education.map(edu => ({
                institution: edu.institution || '',
                course: edu.course || '',
                education_level: edu.education_level || '',
                date_from: edu.date_from || '',
                date_to: edu.date_to || '',
                units: edu.units || '',
                duration_years: calculateDuration(edu.date_from, edu.date_to),
                created_at: edu.created_at || '',
                updated_at: edu.updated_at || '',
                id: edu.id || '',
                applicant_id: edu.applicant_id || ''
            })),
            highest_qualification: getHighestQualification(education),
            education_summary: generateEducationSummary(education),
            complete_education_records: education,
            education_timeline: education.map(edu => ({
                period: `${edu.date_from || 'Unknown'} to ${edu.date_to || 'Present'}`,
                institution: edu.institution || '',
                qualification: `${edu.education_level || ''} in ${edu.course || ''}`,
                units_studied: edu.units || '',
                duration: calculateDuration(edu.date_from, edu.date_to) + ' years'
            }))
        },
        work_experience: {
            total_experiences: experiences.length,
            total_years_experience: calculateTotalExperience(experiences),
            experiences: experiences.map(exp => ({
                employer: exp.employer || '',
                position: exp.position || '',
                date_from: exp.date_from || '',
                date_to: exp.date_to || '',
                duration_years: calculateDuration(exp.date_from, exp.date_to),
                work_description: exp.work_description || '',
                achievements: exp.achievements || '',
                employer_contacts: exp.employer_contacts_address || '',
                created_at: exp.created_at || '',
                updated_at: exp.updated_at || '',
                id: exp.id || '',
                applicant_id: exp.applicant_id || ''
            })),
            current_employment: getCurrentEmployment(experiences),
            career_progression: analyzeCareerProgression(experiences),
            complete_experience_records: experiences,
            experience_timeline: experiences.map(exp => ({
                period: `${exp.date_from || 'Unknown'} to ${exp.date_to || 'Present'}`,
                employer: exp.employer || '',
                position: exp.position || '',
                duration: calculateDuration(exp.date_from, exp.date_to) + ' years',
                full_work_description: exp.work_description || '',
                full_achievements: exp.achievements || '',
                employer_contact_details: exp.employer_contacts_address || ''
            }))
        },
        supporting_documents: {
            total_files: files.length,
            documents: files.map(file => ({
                title: file.file_title || '',
                description: file.file_description || '',
                file_path: file.file_path || '',
                extracted_text_available: !!(file.file_extracted_texts),
                extracted_text_length: file.file_extracted_texts ? file.file_extracted_texts.length : 0,
                upload_date: file.created_at || '',
                full_extracted_text: file.file_extracted_texts || ''
            })),
            document_summary: generateDocumentSummary(files),
            all_extracted_texts_combined: files.map(file => ({
                file_title: file.file_title || '',
                extracted_content: file.file_extracted_texts || ''
            })).filter(item => item.extracted_content)
        },
        profile_analysis: {
            completeness_score: calculateCompletenessScore(personalInfo, education, experiences, files),
            strengths: identifyStrengths(personalInfo, education, experiences),
            areas_for_development: identifyAreasForDevelopment(personalInfo, education, experiences),
            suitability_assessment: generateSuitabilityAssessment(personalInfo, education, experiences),
            key_qualifications: extractKeyQualifications(education, experiences),
            professional_summary: generateProfessionalSummary(personalInfo, education, experiences)
        },
        data_integrity: {
            personal_info_complete: checkPersonalInfoCompleteness(personalInfo),
            education_records_available: education.length > 0,
            experience_records_available: experiences.length > 0,
            supporting_documents_available: files.length > 0,
            mandatory_fields_status: checkMandatoryFields(personalInfo)
        },
        complete_raw_data: {
            personal_information_complete: personalInfo,
            education_records_complete: education,
            experience_records_complete: experiences,
            files_records_complete: files,
            all_extracted_texts: files.map(file => ({
                file_id: file.id || '',
                file_title: file.file_title || '',
                file_description: file.file_description || '',
                file_path: file.file_path || '',
                upload_date: file.created_at || '',
                complete_extracted_text: file.file_extracted_texts || 'No text extracted'
            }))
        },
        comprehensive_applicant_profile: {
            full_name_details: {
                first_name: personalInfo.first_name || '',
                last_name: personalInfo.last_name || '',
                full_name: `${personalInfo.first_name || ''} ${personalInfo.last_name || ''}`.trim()
            },
            complete_contact_information: {
                email: personalInfo.email || '',
                contact_details: personalInfo.contact_details || '',
                location_address: personalInfo.location_address || '',
                place_of_origin: personalInfo.place_of_origin || ''
            },
            complete_personal_details: {
                unique_id: personalInfo.unique_id || '',
                gender: personalInfo.gender || '',
                date_of_birth: personalInfo.dobirth || '',
                citizenship: personalInfo.citizenship || '',
                marital_status: personalInfo.marital_status || '',
                date_of_marriage: personalInfo.date_of_marriage || '',
                spouse_employer: personalInfo.spouse_employer || '',
                children_details: personalInfo.children || ''
            },
            complete_identification: {
                id_numbers: personalInfo.id_numbers || '',
                id_photo_path: personalInfo.id_photo_path || '',
                signature_path: personalInfo.signature_path || ''
            },
            complete_employment_information: {
                is_public_servant: personalInfo.is_public_servant || '',
                public_service_file_number: personalInfo.public_service_file_number || '',
                employee_of_org_id: personalInfo.employee_of_org_id || '',
                current_employer: personalInfo.current_employer || '',
                current_position: personalInfo.current_position || '',
                current_salary: personalInfo.current_salary || ''
            },
            complete_additional_information: {
                offence_convicted: personalInfo.offence_convicted || '',
                how_did_you_hear_about_us: personalInfo.how_did_you_hear_about_us || '',
                publications: personalInfo.publications || '',
                awards: personalInfo.awards || '',
                referees: personalInfo.referees || ''
            },
            all_education_qualifications_detailed: education.map(edu => ({
                qualification_id: edu.id || '',
                institution_name: edu.institution || '',
                course_name: edu.course || '',
                education_level: edu.education_level || '',
                start_date: edu.date_from || '',
                end_date: edu.date_to || '',
                units_subjects_studied: edu.units || '',
                duration_calculated: calculateDuration(edu.date_from, edu.date_to) + ' years',
                record_created: edu.created_at || '',
                record_updated: edu.updated_at || ''
            })),
            all_work_experiences_detailed: experiences.map(exp => ({
                experience_id: exp.id || '',
                employer_name: exp.employer || '',
                position_title: exp.position || '',
                employment_start_date: exp.date_from || '',
                employment_end_date: exp.date_to || '',
                duration_calculated: calculateDuration(exp.date_from, exp.date_to) + ' years',
                complete_work_description: exp.work_description || '',
                complete_achievements: exp.achievements || '',
                employer_contact_address: exp.employer_contacts_address || '',
                record_created: exp.created_at || '',
                record_updated: exp.updated_at || ''
            })),
            all_supporting_documents_with_full_texts: files.map(file => ({
                document_id: file.id || '',
                document_title: file.file_title || '',
                document_description: file.file_description || '',
                file_path: file.file_path || '',
                upload_timestamp: file.created_at || '',
                last_updated: file.updated_at || '',
                full_extracted_text_content: file.file_extracted_texts || 'No text content available'
            }))
        }
    };

    return profile;
}

// Helper functions for profile generation
function calculateDuration(dateFrom, dateTo) {
    if (!dateFrom) return 0;

    const startDate = new Date(dateFrom);
    const endDate = dateTo ? new Date(dateTo) : new Date();

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) return 0;

    const diffTime = Math.abs(endDate - startDate);
    const diffYears = diffTime / (1000 * 60 * 60 * 24 * 365.25);

    return Math.round(diffYears * 10) / 10; // Round to 1 decimal place
}

function calculateTotalExperience(experiences) {
    return experiences.reduce((total, exp) => {
        return total + calculateDuration(exp.date_from, exp.date_to);
    }, 0);
}

function getHighestQualification(education) {
    if (!education || education.length === 0) return 'No education records';

    // Define education level hierarchy
    const levelHierarchy = {
        'primary': 1,
        'secondary': 2,
        'certificate': 3,
        'diploma': 4,
        'degree': 5,
        'masters': 6,
        'doctorate': 7,
        'phd': 7
    };

    let highest = education[0];
    let highestLevel = 0;

    education.forEach(edu => {
        const level = levelHierarchy[edu.education_level?.toLowerCase()] || 0;
        if (level > highestLevel) {
            highestLevel = level;
            highest = edu;
        }
    });

    return `${highest.education_level || 'Unknown'} - ${highest.course || 'Unknown Course'} from ${highest.institution || 'Unknown Institution'}`;
}

function generateEducationSummary(education) {
    if (!education || education.length === 0) return 'No education records available';

    const summary = [];
    const levels = {};

    education.forEach(edu => {
        const level = edu.education_level || 'Unknown';
        if (!levels[level]) levels[level] = 0;
        levels[level]++;
    });

    Object.keys(levels).forEach(level => {
        summary.push(`${levels[level]} ${level} qualification${levels[level] > 1 ? 's' : ''}`);
    });

    return summary.join(', ');
}

function getCurrentEmployment(experiences) {
    if (!experiences || experiences.length === 0) return null;

    // Find current employment (no end date or most recent)
    const current = experiences.find(exp => !exp.date_to) ||
                   experiences.sort((a, b) => new Date(b.date_from) - new Date(a.date_from))[0];

    return current ? {
        employer: current.employer,
        position: current.position,
        start_date: current.date_from,
        is_current: !current.date_to
    } : null;
}

function analyzeCareerProgression(experiences) {
    if (!experiences || experiences.length < 2) return 'Insufficient data for career progression analysis';

    const sortedExp = experiences.sort((a, b) => new Date(a.date_from) - new Date(b.date_from));
    const progression = [];

    for (let i = 1; i < sortedExp.length; i++) {
        const prev = sortedExp[i-1];
        const curr = sortedExp[i];
        progression.push(`From ${prev.position} at ${prev.employer} to ${curr.position} at ${curr.employer}`);
    }

    return progression.join(' → ');
}

function generateDocumentSummary(files) {
    if (!files || files.length === 0) return 'No supporting documents uploaded';

    const summary = {
        total_files: files.length,
        files_with_extracted_text: files.filter(f => f.file_extracted_texts).length,
        document_types: files.map(f => f.file_title).join(', ')
    };

    return `${summary.total_files} documents uploaded (${summary.files_with_extracted_text} with extracted text): ${summary.document_types}`;
}

function calculateCompletenessScore(personalInfo, education, experiences, files) {
    let score = 0;
    let maxScore = 100;

    // Personal info (40 points)
    const requiredPersonalFields = ['first_name', 'last_name', 'email', 'gender', 'dobirth', 'contact_details', 'location_address', 'citizenship'];
    const personalScore = (requiredPersonalFields.filter(field => personalInfo[field]).length / requiredPersonalFields.length) * 40;
    score += personalScore;

    // Education (25 points)
    const educationScore = education.length > 0 ? 25 : 0;
    score += educationScore;

    // Experience (25 points)
    const experienceScore = experiences.length > 0 ? 25 : 0;
    score += experienceScore;

    // Documents (10 points)
    const documentScore = files.length > 0 ? 10 : 0;
    score += documentScore;

    return Math.round(score);
}

function updateProcessingStatus(message, progress) {
    $('#processingMessage').text(message);
    $('#processingProgress').css('width', progress + '%');
}

function identifyStrengths(personalInfo, education, experiences) {
    const strengths = [];

    // Education strengths
    if (education.length > 0) {
        strengths.push(`Has ${education.length} educational qualification${education.length > 1 ? 's' : ''}`);
        if (education.some(e => e.education_level?.toLowerCase().includes('degree'))) {
            strengths.push('Holds university degree qualification');
        }
    }

    // Experience strengths
    const totalExp = calculateTotalExperience(experiences);
    if (totalExp > 0) {
        strengths.push(`${totalExp} years of professional experience`);
    }
    if (experiences.length > 2) {
        strengths.push('Diverse work experience across multiple organizations');
    }

    // Personal strengths
    if (personalInfo.publications) {
        strengths.push('Has publications/research work');
    }
    if (personalInfo.awards) {
        strengths.push('Has received awards/recognition');
    }

    return strengths.length > 0 ? strengths : ['Profile demonstrates basic qualifications'];
}

function identifyAreasForDevelopment(personalInfo, education, experiences) {
    const areas = [];

    if (!education || education.length === 0) {
        areas.push('No formal education records provided');
    }
    if (!experiences || experiences.length === 0) {
        areas.push('No work experience records provided');
    }
    if (!personalInfo.publications) {
        areas.push('Could benefit from research/publication activities');
    }
    if (!personalInfo.awards) {
        areas.push('Could pursue professional recognition/awards');
    }

    return areas.length > 0 ? areas : ['Profile appears comprehensive'];
}

function generateSuitabilityAssessment(personalInfo, education, experiences) {
    const score = calculateCompletenessScore(personalInfo, education, experiences, []);

    if (score >= 80) return 'Highly suitable candidate with comprehensive profile';
    if (score >= 60) return 'Suitable candidate with good qualifications';
    if (score >= 40) return 'Potentially suitable candidate, some areas need attention';
    return 'Profile needs significant improvement for better suitability';
}

function extractKeyQualifications(education, experiences) {
    const qualifications = [];

    // Education qualifications
    education.forEach(edu => {
        if (edu.course && edu.education_level) {
            qualifications.push(`${edu.education_level} in ${edu.course}`);
        }
    });

    // Experience qualifications
    const uniquePositions = [...new Set(experiences.map(exp => exp.position).filter(p => p))];
    qualifications.push(...uniquePositions.map(pos => `Experience as ${pos}`));

    return qualifications;
}

function generateProfessionalSummary(personalInfo, education, experiences) {
    const name = `${personalInfo.first_name || ''} ${personalInfo.last_name || ''}`.trim();
    const totalExp = calculateTotalExperience(experiences);
    const highestEd = getHighestQualification(education);

    let summary = `${name} is a professional with `;

    if (totalExp > 0) {
        summary += `${totalExp} years of experience `;
    }

    if (education.length > 0) {
        summary += `holding ${highestEd}. `;
    }

    if (experiences.length > 0) {
        const currentJob = getCurrentEmployment(experiences);
        if (currentJob && currentJob.is_current) {
            summary += `Currently employed as ${currentJob.position} at ${currentJob.employer}.`;
        }
    }

    return summary;
}

function checkPersonalInfoCompleteness(personalInfo) {
    const requiredFields = ['first_name', 'last_name', 'email', 'gender', 'dobirth', 'contact_details', 'location_address', 'citizenship'];
    const completedFields = requiredFields.filter(field => personalInfo[field]);
    return {
        completed_fields: completedFields.length,
        total_required: requiredFields.length,
        percentage: Math.round((completedFields.length / requiredFields.length) * 100),
        missing_fields: requiredFields.filter(field => !personalInfo[field])
    };
}

function checkMandatoryFields(personalInfo) {
    const mandatoryFields = {
        'name': !!(personalInfo.first_name && personalInfo.last_name),
        'gender': !!personalInfo.gender,
        'date_of_birth': !!personalInfo.dobirth,
        'contacts': !!personalInfo.contact_details,
        'email': !!personalInfo.email,
        'current_address': !!personalInfo.location_address,
        'place_of_origin': !!personalInfo.place_of_origin,
        'citizenship': !!personalInfo.citizenship,
        'id_numbers': !!personalInfo.id_numbers,
        'current_employer': !!personalInfo.current_employer,
        'current_position': !!personalInfo.current_position,
        'current_salary': !!personalInfo.current_salary,
        'referees': !!personalInfo.referees
    };

    const completed = Object.values(mandatoryFields).filter(Boolean).length;
    const total = Object.keys(mandatoryFields).length;

    return {
        fields_status: mandatoryFields,
        completed_count: completed,
        total_count: total,
        completion_percentage: Math.round((completed / total) * 100)
    };
}

function updateStepStatus(stepNumber, status, text) {
    const step = $(`#step${stepNumber}`);
    const icon = step.find('.step-icon i');

    // Reset classes
    icon.removeClass('fas fa-check text-success fa-clock text-warning fa-times text-danger fa-spinner fa-spin text-primary text-muted');

    switch (status) {
        case 'complete':
            icon.addClass('fas fa-check text-success');
            break;
        case 'processing':
            icon.addClass('fas fa-spinner fa-spin text-primary');
            break;
        case 'ready':
            icon.addClass('fas fa-clock text-warning');
            break;
        case 'error':
            icon.addClass('fas fa-times text-danger');
            break;
        default:
            icon.addClass('fas fa-clock text-muted');
    }

    if (text) {
        step.find('.step-text').text(text);
    }
}
</script>
<?= $this->endSection() ?>
